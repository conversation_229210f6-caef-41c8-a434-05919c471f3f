
var skwasm_st = (() => {
  var _scriptName = typeof document != 'undefined' ? document.currentScript?.src : undefined;
  
  return (
function(moduleArg = {}) {
  var moduleRtn;

function d(){g.buffer!=k.buffer&&n();return k}function q(){g.buffer!=k.buffer&&n();return aa}function r(){g.buffer!=k.buffer&&n();return ba}function t(){g.buffer!=k.buffer&&n();return ca}function u(){g.buffer!=k.buffer&&n();return da}var w=moduleArg,ea,fa,ha=new Promise((a,b)=>{ea=a;fa=b}),ia="object"==typeof window,ja="function"==typeof importScripts,ka=Object.assign({},w),x="",la,ma;
if(ia||ja)ja?x=self.location.href:"undefined"!=typeof document&&document.currentScript&&(x=document.currentScript.src),_scriptName&&(x=_scriptName),x.startsWith("blob:")?x="":x=x.substr(0,x.replace(/[?#].*/,"").lastIndexOf("/")+1),ja&&(ma=a=>{var b=new XMLHttpRequest;b.open("GET",a,!1);b.responseType="arraybuffer";b.send(null);return new Uint8Array(b.response)}),la=a=>fetch(a,{credentials:"same-origin"}).then(b=>b.ok?b.arrayBuffer():Promise.reject(Error(b.status+" : "+b.url)));
var na=console.log.bind(console),y=console.error.bind(console);Object.assign(w,ka);ka=null;var g,oa=!1,pa,k,aa,qa,ra,ba,ca,da;function n(){var a=g.buffer;k=new Int8Array(a);qa=new Int16Array(a);aa=new Uint8Array(a);ra=new Uint16Array(a);ba=new Int32Array(a);ca=new Uint32Array(a);da=new Float32Array(a);new Float64Array(a)}var sa=[],ta=[],ua=[],z=0,va=null,A=null;
function wa(a){a="Aborted("+a+")";y(a);oa=!0;a=new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");fa(a);throw a;}var xa=a=>a.startsWith("data:application/octet-stream;base64,"),ya;function za(a){return la(a).then(b=>new Uint8Array(b),()=>{if(ma)var b=ma(a);else throw"both async and sync fetching of the wasm failed";return b})}function Aa(a,b,c){return za(a).then(e=>WebAssembly.instantiate(e,b)).then(c,e=>{y(`failed to asynchronously prepare wasm: ${e}`);wa(e)})}
function Ba(a,b){var c=ya;return"function"!=typeof WebAssembly.instantiateStreaming||xa(c)||"function"!=typeof fetch?Aa(c,a,b):fetch(c,{credentials:"same-origin"}).then(e=>WebAssembly.instantiateStreaming(e,a).then(b,function(f){y(`wasm streaming compile failed: ${f}`);y("falling back to ArrayBuffer instantiation");return Aa(c,a,b)}))}function Ca(a){this.name="ExitStatus";this.message=`Program terminated with exit(${a})`;this.status=a}var Da=a=>{a.forEach(b=>b(w))},Ea=w.noExitRuntime||!0;
class Fa{constructor(a){this.s=a-24}}
var Ga=0,Ha=0,Ia="undefined"!=typeof TextDecoder?new TextDecoder:void 0,Ja=(a,b=0,c=NaN)=>{var e=b+c;for(c=b;a[c]&&!(c>=e);)++c;if(16<c-b&&a.buffer&&Ia)return Ia.decode(a.slice(b,c));for(e="";b<c;){var f=a[b++];if(f&128){var h=a[b++]&63;if(192==(f&224))e+=String.fromCharCode((f&31)<<6|h);else{var l=a[b++]&63;f=224==(f&240)?(f&15)<<12|h<<6|l:(f&7)<<18|h<<12|l<<6|a[b++]&63;65536>f?e+=String.fromCharCode(f):(f-=65536,e+=String.fromCharCode(55296|f>>10,56320|f&1023))}}else e+=String.fromCharCode(f)}return e},
Ka=(a,b)=>a?Ja(q(),a,b):"",B={},La=a=>{if(!(a instanceof Ca||"unwind"==a))throw a;},Ma=0,Na=a=>{pa=a;Ea||0<Ma||(oa=!0);throw new Ca(a);},Oa=a=>{if(!oa)try{if(a(),!(Ea||0<Ma))try{pa=a=pa,Na(a)}catch(b){La(b)}}catch(b){La(b)}},C=(a,b,c)=>{var e=q();if(0<c){var f=b;c=b+c-1;for(var h=0;h<a.length;++h){var l=a.charCodeAt(h);if(55296<=l&&57343>=l){var m=a.charCodeAt(++h);l=65536+((l&1023)<<10)|m&1023}if(127>=l){if(b>=c)break;e[b++]=l}else{if(2047>=l){if(b+1>=c)break;e[b++]=192|l>>6}else{if(65535>=l){if(b+
2>=c)break;e[b++]=224|l>>12}else{if(b+3>=c)break;e[b++]=240|l>>18;e[b++]=128|l>>12&63}e[b++]=128|l>>6&63}e[b++]=128|l&63}}e[b]=0;a=b-f}else a=0;return a},D,Pa=a=>{var b=a.getExtension("ANGLE_instanced_arrays");b&&(a.vertexAttribDivisor=(c,e)=>b.vertexAttribDivisorANGLE(c,e),a.drawArraysInstanced=(c,e,f,h)=>b.drawArraysInstancedANGLE(c,e,f,h),a.drawElementsInstanced=(c,e,f,h,l)=>b.drawElementsInstancedANGLE(c,e,f,h,l))},Qa=a=>{var b=a.getExtension("OES_vertex_array_object");b&&(a.createVertexArray=
()=>b.createVertexArrayOES(),a.deleteVertexArray=c=>b.deleteVertexArrayOES(c),a.bindVertexArray=c=>b.bindVertexArrayOES(c),a.isVertexArray=c=>b.isVertexArrayOES(c))},Ra=a=>{var b=a.getExtension("WEBGL_draw_buffers");b&&(a.drawBuffers=(c,e)=>b.drawBuffersWEBGL(c,e))},Sa=a=>{a.H=a.getExtension("WEBGL_draw_instanced_base_vertex_base_instance")},Ta=a=>{a.K=a.getExtension("WEBGL_multi_draw_instanced_base_vertex_base_instance")},Ua=a=>{var b="ANGLE_instanced_arrays EXT_blend_minmax EXT_disjoint_timer_query EXT_frag_depth EXT_shader_texture_lod EXT_sRGB OES_element_index_uint OES_fbo_render_mipmap OES_standard_derivatives OES_texture_float OES_texture_half_float OES_texture_half_float_linear OES_vertex_array_object WEBGL_color_buffer_float WEBGL_depth_texture WEBGL_draw_buffers EXT_color_buffer_float EXT_conservative_depth EXT_disjoint_timer_query_webgl2 EXT_texture_norm16 NV_shader_noperspective_interpolation WEBGL_clip_cull_distance EXT_clip_control EXT_color_buffer_half_float EXT_depth_clamp EXT_float_blend EXT_polygon_offset_clamp EXT_texture_compression_bptc EXT_texture_compression_rgtc EXT_texture_filter_anisotropic KHR_parallel_shader_compile OES_texture_float_linear WEBGL_blend_func_extended WEBGL_compressed_texture_astc WEBGL_compressed_texture_etc WEBGL_compressed_texture_etc1 WEBGL_compressed_texture_s3tc WEBGL_compressed_texture_s3tc_srgb WEBGL_debug_renderer_info WEBGL_debug_shaders WEBGL_lose_context WEBGL_multi_draw WEBGL_polygon_mode".split(" ");
return(a.getSupportedExtensions()||[]).filter(c=>b.includes(c))},Va=1,Wa=[],E=[],Xa=[],F=[],G=[],H=[],Ya=[],Za=[],I=[],J=[],K=[],$a={},ab={},bb=4,cb=0,L=a=>{for(var b=Va++,c=a.length;c<b;c++)a[c]=null;return b},N=(a,b,c,e)=>{for(var f=0;f<a;f++){var h=D[c](),l=h&&L(e);h?(h.name=l,e[l]=h):M||=1282;r()[b+4*f>>2]=l}},eb=a=>{var b={J:2,alpha:!0,depth:!0,stencil:!0,antialias:!1,premultipliedAlpha:!0,preserveDrawingBuffer:!1,powerPreference:"default",failIfMajorPerformanceCaveat:!1,I:!0};a.s||(a.s=a.getContext,
a.getContext=function(e,f){f=a.s(e,f);return"webgl"==e==f instanceof WebGLRenderingContext?f:null});var c=1<b.J?a.getContext("webgl2",b):a.getContext("webgl",b);return c?db(c,b):0},db=(a,b)=>{var c=L(Za),e={handle:c,attributes:b,version:b.J,v:a};a.canvas&&(a.canvas.Z=e);Za[c]=e;("undefined"==typeof b.I||b.I)&&fb(e);return c},fb=a=>{a||=O;if(!a.S){a.S=!0;var b=a.v;b.T=b.getExtension("WEBGL_multi_draw");b.P=b.getExtension("EXT_polygon_offset_clamp");b.O=b.getExtension("EXT_clip_control");b.Y=b.getExtension("WEBGL_polygon_mode");
Pa(b);Qa(b);Ra(b);Sa(b);Ta(b);2<=a.version&&(b.g=b.getExtension("EXT_disjoint_timer_query_webgl2"));if(2>a.version||!b.g)b.g=b.getExtension("EXT_disjoint_timer_query");Ua(b).forEach(c=>{c.includes("lose_context")||c.includes("debug")||b.getExtension(c)})}},M,O,gb=a=>{D.bindVertexArray(Ya[a])},hb=(a,b)=>{for(var c=0;c<a;c++){var e=r()[b+4*c>>2],f=G[e];f&&(D.deleteTexture(f),f.name=0,G[e]=null)}},ib=(a,b)=>{for(var c=0;c<a;c++){var e=r()[b+4*c>>2];D.deleteVertexArray(Ya[e]);Ya[e]=null}},jb=[],kb=(a,
b)=>{N(a,b,"createVertexArray",Ya)},lb=(a,b)=>{t()[a>>2]=b;var c=t()[a>>2];t()[a+4>>2]=(b-c)/4294967296};function mb(){var a=Ua(D);return a=a.concat(a.map(b=>"GL_"+b))}
var nb=(a,b,c)=>{if(b){var e=void 0;switch(a){case 36346:e=1;break;case 36344:0!=c&&1!=c&&(M||=1280);return;case 34814:case 36345:e=0;break;case 34466:var f=D.getParameter(34467);e=f?f.length:0;break;case 33309:if(2>O.version){M||=1282;return}e=mb().length;break;case 33307:case 33308:if(2>O.version){M||=1280;return}e=33307==a?3:0}if(void 0===e)switch(f=D.getParameter(a),typeof f){case "number":e=f;break;case "boolean":e=f?1:0;break;case "string":M||=1280;return;case "object":if(null===f)switch(a){case 34964:case 35725:case 34965:case 36006:case 36007:case 32873:case 34229:case 36662:case 36663:case 35053:case 35055:case 36010:case 35097:case 35869:case 32874:case 36389:case 35983:case 35368:case 34068:e=
0;break;default:M||=1280;return}else{if(f instanceof Float32Array||f instanceof Uint32Array||f instanceof Int32Array||f instanceof Array){for(a=0;a<f.length;++a)switch(c){case 0:r()[b+4*a>>2]=f[a];break;case 2:u()[b+4*a>>2]=f[a];break;case 4:d()[b+a]=f[a]?1:0}return}try{e=f.name|0}catch(h){M||=1280;y(`GL_INVALID_ENUM in glGet${c}v: Unknown object returned from WebGL getParameter(${a})! (error: ${h})`);return}}break;default:M||=1280;y(`GL_INVALID_ENUM in glGet${c}v: Native code calling glGet${c}v(${a}) and it returns ${f} of type ${typeof f}!`);
return}switch(c){case 1:lb(b,e);break;case 0:r()[b>>2]=e;break;case 2:u()[b>>2]=e;break;case 4:d()[b]=e?1:0}}else M||=1281},ob=(a,b)=>nb(a,b,0),pb=(a,b,c)=>{if(c){a=I[a];b=2>O.version?D.g.getQueryObjectEXT(a,b):D.getQueryParameter(a,b);var e;"boolean"==typeof b?e=b?1:0:e=b;lb(c,e)}else M||=1281},rb=a=>{for(var b=0,c=0;c<a.length;++c){var e=a.charCodeAt(c);127>=e?b++:2047>=e?b+=2:55296<=e&&57343>=e?(b+=4,++c):b+=3}b+=1;(c=qb(b))&&C(a,c,b);return c},sb=a=>{var b=$a[a];if(!b){switch(a){case 7939:b=rb(mb().join(" "));
break;case 7936:case 7937:case 37445:case 37446:(b=D.getParameter(a))||(M||=1280);b=b?rb(b):0;break;case 7938:b=D.getParameter(7938);var c=`OpenGL ES 2.0 (${b})`;2<=O.version&&(c=`OpenGL ES 3.0 (${b})`);b=rb(c);break;case 35724:b=D.getParameter(35724);c=b.match(/^WebGL GLSL ES ([0-9]\.[0-9][0-9]?)(?:$| .*)/);null!==c&&(3==c[1].length&&(c[1]+="0"),b=`OpenGL ES GLSL ES ${c[1]} (${b})`);b=rb(b);break;default:M||=1280}$a[a]=b}return b},tb=(a,b)=>{if(2>O.version)return M||=1282,0;var c=ab[a];if(c)return 0>
b||b>=c.length?(M||=1281,0):c[b];switch(a){case 7939:return c=mb().map(rb),c=ab[a]=c,0>b||b>=c.length?(M||=1281,0):c[b];default:return M||=1280,0}},ub=a=>"]"==a.slice(-1)&&a.lastIndexOf("["),vb=a=>{a-=5120;0==a?a=d():1==a?a=q():2==a?(g.buffer!=k.buffer&&n(),a=qa):4==a?a=r():6==a?a=u():5==a||28922==a||28520==a||30779==a||30782==a?a=t():(g.buffer!=k.buffer&&n(),a=ra);return a},wb=(a,b,c,e,f)=>{a=vb(a);b=e*((cb||c)*({5:3,6:4,8:2,29502:3,29504:4,26917:2,26918:2,29846:3,29847:4}[b-6402]||1)*a.BYTES_PER_ELEMENT+
bb-1&-bb);return a.subarray(f>>>31-Math.clz32(a.BYTES_PER_ELEMENT),f+b>>>31-Math.clz32(a.BYTES_PER_ELEMENT))},P=a=>{var b=D.N;if(b){var c=b.u[a];"number"==typeof c&&(b.u[a]=c=D.getUniformLocation(b,b.L[a]+(0<c?`[${c}]`:"")));return c}M||=1282},Q=[],xb=[],yb={},Ab=()=>{if(!zb){var a={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:"./this.program"},b;for(b in yb)void 0===
yb[b]?delete a[b]:a[b]=yb[b];var c=[];for(b in a)c.push(`${b}=${a[b]}`);zb=c}return zb},zb,Bb=[null,[],[]];function Cb(){}function Db(){}function R(){}function Eb(){}function Fb(){}function Gb(){}function Hb(){}function Ib(){}function Jb(){}function Kb(){}function Lb(){}function Mb(){}function Nb(){}function Ob(){}function Pb(){}function Qb(){}var S,U,Rb=[],Tb=a=>Sb(a);w.stackAlloc=Tb;for(var V=0;32>V;++V)jb.push(Array(V));var Ub=new Float32Array(288);for(V=0;288>=V;++V)Q[V]=Ub.subarray(0,V);
var Vb=new Int32Array(288);for(V=0;288>=V;++V)xb[V]=Vb.subarray(0,V);
(function(){const a=new Map,b=new Map;Qb=function(c,e,f){R({m:"setAssociatedObject",F:e,object:f})};Mb=function(c){return b.get(c)};Fb=function(){Cb(function(c){var e=c.m;if(e)switch(e){case "renderPictures":Wb(c.h,c.V,c.U,c.l,Db());break;case "onRenderComplete":Xb(c.h,c.l,{imageBitmaps:c.R,rasterStartMilliseconds:c.X,rasterEndMilliseconds:c.W});break;case "setAssociatedObject":b.set(c.F,c.object);break;case "disposeAssociatedObject":c=c.F;e=b.get(c);e.close&&e.close();b.delete(c);break;case "disposeSurface":Yb(c.h);
break;case "rasterizeImage":Zb(c.h,c.image,c.format,c.l);break;case "onRasterizeComplete":$b(c.h,c.data,c.l);break;default:console.warn(`unrecognized skwasm message: ${e}`)}})};Kb=function(c,e,f,h,l){R({m:"renderPictures",h:e,V:f,U:h,l})};Hb=function(c,e){c=new OffscreenCanvas(c,e);e=eb(c);a.set(e,c);return e};Ob=function(c,e,f){c=a.get(c);c.width=e;c.height=f};Eb=function(c,e,f,h){h||=[];c=a.get(c);h.push(createImageBitmap(c,0,0,e,f));return h};Pb=async function(c,e,f,h){e=e?await Promise.all(e):
[];R({m:"onRenderComplete",h:c,l:h,R:e,X:f,W:Db()},[...e])};Gb=function(c,e,f){const h=O.v,l=h.createTexture();h.bindTexture(h.TEXTURE_2D,l);h.pixelStorei(h.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0);h.texImage2D(h.TEXTURE_2D,0,h.RGBA,e,f,0,h.RGBA,h.UNSIGNED_BYTE,c);h.pixelStorei(h.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1);h.bindTexture(h.TEXTURE_2D,null);c=L(G);G[c]=l;return c};Lb=function(c,e){R({m:"disposeAssociatedObject",F:e})};Ib=function(c,e){R({m:"disposeSurface",h:e})};Jb=function(c,e,f,h,l){R({m:"rasterizeImage",
h:e,image:f,format:h,l})};Nb=function(c,e,f){R({m:"onRasterizeComplete",h:c,data:e,l:f})}})();(function(){let a;Cb=function(b){a=b};Db=function(){return performance.now()};R=function(b){queueMicrotask(()=>{a(b)})}})();
var lc={__cxa_throw:(a,b,c)=>{var e=new Fa(a);t()[e.s+16>>2]=0;t()[e.s+4>>2]=b;t()[e.s+8>>2]=c;Ga=a;Ha++;throw Ga;},__syscall_fcntl64:function(){return 0},__syscall_fstat64:()=>{},__syscall_ioctl:function(){return 0},__syscall_openat:function(){},_abort_js:()=>{wa("")},_emscripten_get_now_is_monotonic:()=>1,_emscripten_runtime_keepalive_clear:()=>{Ea=!1;Ma=0},_emscripten_throw_longjmp:()=>{throw Infinity;},_mmap_js:function(){return-52},_munmap_js:function(){},_setitimer_js:(a,b)=>{B[a]&&(clearTimeout(B[a].id),
delete B[a]);if(!b)return 0;var c=setTimeout(()=>{delete B[a];Oa(()=>ac(a,performance.now()))},b);B[a]={id:c,aa:b};return 0},_tzset_js:(a,b,c,e)=>{var f=(new Date).getFullYear(),h=(new Date(f,0,1)).getTimezoneOffset();f=(new Date(f,6,1)).getTimezoneOffset();var l=Math.max(h,f);t()[a>>2]=60*l;r()[b>>2]=Number(h!=f);b=m=>{var p=Math.abs(m);return`UTC${0<=m?"-":"+"}${String(Math.floor(p/60)).padStart(2,"0")}${String(p%60).padStart(2,"0")}`};a=b(h);b=b(f);f<h?(C(a,c,17),C(b,e,17)):(C(a,e,17),C(b,c,17))},
emscripten_get_now:()=>performance.now(),emscripten_glActiveTexture:a=>D.activeTexture(a),emscripten_glAttachShader:(a,b)=>{D.attachShader(E[a],H[b])},emscripten_glBeginQuery:(a,b)=>{D.beginQuery(a,I[b])},emscripten_glBeginQueryEXT:(a,b)=>{D.g.beginQueryEXT(a,I[b])},emscripten_glBindAttribLocation:(a,b,c)=>{D.bindAttribLocation(E[a],b,Ka(c))},emscripten_glBindBuffer:(a,b)=>{35051==a?D.D=b:35052==a&&(D.o=b);D.bindBuffer(a,Wa[b])},emscripten_glBindFramebuffer:(a,b)=>{D.bindFramebuffer(a,Xa[b])},emscripten_glBindRenderbuffer:(a,
b)=>{D.bindRenderbuffer(a,F[b])},emscripten_glBindSampler:(a,b)=>{D.bindSampler(a,J[b])},emscripten_glBindTexture:(a,b)=>{D.bindTexture(a,G[b])},emscripten_glBindVertexArray:gb,emscripten_glBindVertexArrayOES:gb,emscripten_glBlendColor:(a,b,c,e)=>D.blendColor(a,b,c,e),emscripten_glBlendEquation:a=>D.blendEquation(a),emscripten_glBlendFunc:(a,b)=>D.blendFunc(a,b),emscripten_glBlitFramebuffer:(a,b,c,e,f,h,l,m,p,v)=>D.blitFramebuffer(a,b,c,e,f,h,l,m,p,v),emscripten_glBufferData:(a,b,c,e)=>{2<=O.version?
c&&b?D.bufferData(a,q(),e,c,b):D.bufferData(a,b,e):D.bufferData(a,c?q().subarray(c,c+b):b,e)},emscripten_glBufferSubData:(a,b,c,e)=>{2<=O.version?c&&D.bufferSubData(a,b,q(),e,c):D.bufferSubData(a,b,q().subarray(e,e+c))},emscripten_glCheckFramebufferStatus:a=>D.checkFramebufferStatus(a),emscripten_glClear:a=>D.clear(a),emscripten_glClearColor:(a,b,c,e)=>D.clearColor(a,b,c,e),emscripten_glClearStencil:a=>D.clearStencil(a),emscripten_glClientWaitSync:(a,b,c,e)=>D.clientWaitSync(K[a],b,(c>>>0)+4294967296*
e),emscripten_glColorMask:(a,b,c,e)=>{D.colorMask(!!a,!!b,!!c,!!e)},emscripten_glCompileShader:a=>{D.compileShader(H[a])},emscripten_glCompressedTexImage2D:(a,b,c,e,f,h,l,m)=>{2<=O.version?D.o||!l?D.compressedTexImage2D(a,b,c,e,f,h,l,m):D.compressedTexImage2D(a,b,c,e,f,h,q(),m,l):D.compressedTexImage2D(a,b,c,e,f,h,q().subarray(m,m+l))},emscripten_glCompressedTexSubImage2D:(a,b,c,e,f,h,l,m,p)=>{2<=O.version?D.o||!m?D.compressedTexSubImage2D(a,b,c,e,f,h,l,m,p):D.compressedTexSubImage2D(a,b,c,e,f,h,
l,q(),p,m):D.compressedTexSubImage2D(a,b,c,e,f,h,l,q().subarray(p,p+m))},emscripten_glCopyBufferSubData:(a,b,c,e,f)=>D.copyBufferSubData(a,b,c,e,f),emscripten_glCopyTexSubImage2D:(a,b,c,e,f,h,l,m)=>D.copyTexSubImage2D(a,b,c,e,f,h,l,m),emscripten_glCreateProgram:()=>{var a=L(E),b=D.createProgram();b.name=a;b.C=b.A=b.B=0;b.G=1;E[a]=b;return a},emscripten_glCreateShader:a=>{var b=L(H);H[b]=D.createShader(a);return b},emscripten_glCullFace:a=>D.cullFace(a),emscripten_glDeleteBuffers:(a,b)=>{for(var c=
0;c<a;c++){var e=r()[b+4*c>>2],f=Wa[e];f&&(D.deleteBuffer(f),f.name=0,Wa[e]=null,e==D.D&&(D.D=0),e==D.o&&(D.o=0))}},emscripten_glDeleteFramebuffers:(a,b)=>{for(var c=0;c<a;++c){var e=r()[b+4*c>>2],f=Xa[e];f&&(D.deleteFramebuffer(f),f.name=0,Xa[e]=null)}},emscripten_glDeleteProgram:a=>{if(a){var b=E[a];b?(D.deleteProgram(b),b.name=0,E[a]=null):M||=1281}},emscripten_glDeleteQueries:(a,b)=>{for(var c=0;c<a;c++){var e=r()[b+4*c>>2],f=I[e];f&&(D.deleteQuery(f),I[e]=null)}},emscripten_glDeleteQueriesEXT:(a,
b)=>{for(var c=0;c<a;c++){var e=r()[b+4*c>>2],f=I[e];f&&(D.g.deleteQueryEXT(f),I[e]=null)}},emscripten_glDeleteRenderbuffers:(a,b)=>{for(var c=0;c<a;c++){var e=r()[b+4*c>>2],f=F[e];f&&(D.deleteRenderbuffer(f),f.name=0,F[e]=null)}},emscripten_glDeleteSamplers:(a,b)=>{for(var c=0;c<a;c++){var e=r()[b+4*c>>2],f=J[e];f&&(D.deleteSampler(f),f.name=0,J[e]=null)}},emscripten_glDeleteShader:a=>{if(a){var b=H[a];b?(D.deleteShader(b),H[a]=null):M||=1281}},emscripten_glDeleteSync:a=>{if(a){var b=K[a];b?(D.deleteSync(b),
b.name=0,K[a]=null):M||=1281}},emscripten_glDeleteTextures:hb,emscripten_glDeleteVertexArrays:ib,emscripten_glDeleteVertexArraysOES:ib,emscripten_glDepthMask:a=>{D.depthMask(!!a)},emscripten_glDisable:a=>D.disable(a),emscripten_glDisableVertexAttribArray:a=>{D.disableVertexAttribArray(a)},emscripten_glDrawArrays:(a,b,c)=>{D.drawArrays(a,b,c)},emscripten_glDrawArraysInstanced:(a,b,c,e)=>{D.drawArraysInstanced(a,b,c,e)},emscripten_glDrawArraysInstancedBaseInstanceWEBGL:(a,b,c,e,f)=>{D.H.drawArraysInstancedBaseInstanceWEBGL(a,
b,c,e,f)},emscripten_glDrawBuffers:(a,b)=>{for(var c=jb[a],e=0;e<a;e++)c[e]=r()[b+4*e>>2];D.drawBuffers(c)},emscripten_glDrawElements:(a,b,c,e)=>{D.drawElements(a,b,c,e)},emscripten_glDrawElementsInstanced:(a,b,c,e,f)=>{D.drawElementsInstanced(a,b,c,e,f)},emscripten_glDrawElementsInstancedBaseVertexBaseInstanceWEBGL:(a,b,c,e,f,h,l)=>{D.H.drawElementsInstancedBaseVertexBaseInstanceWEBGL(a,b,c,e,f,h,l)},emscripten_glDrawRangeElements:(a,b,c,e,f,h)=>{D.drawElements(a,e,f,h)},emscripten_glEnable:a=>D.enable(a),
emscripten_glEnableVertexAttribArray:a=>{D.enableVertexAttribArray(a)},emscripten_glEndQuery:a=>D.endQuery(a),emscripten_glEndQueryEXT:a=>{D.g.endQueryEXT(a)},emscripten_glFenceSync:(a,b)=>(a=D.fenceSync(a,b))?(b=L(K),a.name=b,K[b]=a,b):0,emscripten_glFinish:()=>D.finish(),emscripten_glFlush:()=>D.flush(),emscripten_glFramebufferRenderbuffer:(a,b,c,e)=>{D.framebufferRenderbuffer(a,b,c,F[e])},emscripten_glFramebufferTexture2D:(a,b,c,e,f)=>{D.framebufferTexture2D(a,b,c,G[e],f)},emscripten_glFrontFace:a=>
D.frontFace(a),emscripten_glGenBuffers:(a,b)=>{N(a,b,"createBuffer",Wa)},emscripten_glGenFramebuffers:(a,b)=>{N(a,b,"createFramebuffer",Xa)},emscripten_glGenQueries:(a,b)=>{N(a,b,"createQuery",I)},emscripten_glGenQueriesEXT:(a,b)=>{for(var c=0;c<a;c++){var e=D.g.createQueryEXT();if(!e){for(M||=1282;c<a;)r()[b+4*c++>>2]=0;break}var f=L(I);e.name=f;I[f]=e;r()[b+4*c>>2]=f}},emscripten_glGenRenderbuffers:(a,b)=>{N(a,b,"createRenderbuffer",F)},emscripten_glGenSamplers:(a,b)=>{N(a,b,"createSampler",J)},
emscripten_glGenTextures:(a,b)=>{N(a,b,"createTexture",G)},emscripten_glGenVertexArrays:kb,emscripten_glGenVertexArraysOES:kb,emscripten_glGenerateMipmap:a=>D.generateMipmap(a),emscripten_glGetBufferParameteriv:(a,b,c)=>{c?r()[c>>2]=D.getBufferParameter(a,b):M||=1281},emscripten_glGetError:()=>{var a=D.getError()||M;M=0;return a},emscripten_glGetFloatv:(a,b)=>nb(a,b,2),emscripten_glGetFramebufferAttachmentParameteriv:(a,b,c,e)=>{a=D.getFramebufferAttachmentParameter(a,b,c);if(a instanceof WebGLRenderbuffer||
a instanceof WebGLTexture)a=a.name|0;r()[e>>2]=a},emscripten_glGetIntegerv:ob,emscripten_glGetProgramInfoLog:(a,b,c,e)=>{a=D.getProgramInfoLog(E[a]);null===a&&(a="(unknown error)");b=0<b&&e?C(a,e,b):0;c&&(r()[c>>2]=b)},emscripten_glGetProgramiv:(a,b,c)=>{if(c)if(a>=Va)M||=1281;else if(a=E[a],35716==b)a=D.getProgramInfoLog(a),null===a&&(a="(unknown error)"),r()[c>>2]=a.length+1;else if(35719==b){if(!a.C){var e=D.getProgramParameter(a,35718);for(b=0;b<e;++b)a.C=Math.max(a.C,D.getActiveUniform(a,b).name.length+
1)}r()[c>>2]=a.C}else if(35722==b){if(!a.A)for(e=D.getProgramParameter(a,35721),b=0;b<e;++b)a.A=Math.max(a.A,D.getActiveAttrib(a,b).name.length+1);r()[c>>2]=a.A}else if(35381==b){if(!a.B)for(e=D.getProgramParameter(a,35382),b=0;b<e;++b)a.B=Math.max(a.B,D.getActiveUniformBlockName(a,b).length+1);r()[c>>2]=a.B}else r()[c>>2]=D.getProgramParameter(a,b);else M||=1281},emscripten_glGetQueryObjecti64vEXT:pb,emscripten_glGetQueryObjectui64vEXT:pb,emscripten_glGetQueryObjectuiv:(a,b,c)=>{if(c){a=D.getQueryParameter(I[a],
b);var e;"boolean"==typeof a?e=a?1:0:e=a;r()[c>>2]=e}else M||=1281},emscripten_glGetQueryObjectuivEXT:(a,b,c)=>{if(c){a=D.g.getQueryObjectEXT(I[a],b);var e;"boolean"==typeof a?e=a?1:0:e=a;r()[c>>2]=e}else M||=1281},emscripten_glGetQueryiv:(a,b,c)=>{c?r()[c>>2]=D.getQuery(a,b):M||=1281},emscripten_glGetQueryivEXT:(a,b,c)=>{c?r()[c>>2]=D.g.getQueryEXT(a,b):M||=1281},emscripten_glGetRenderbufferParameteriv:(a,b,c)=>{c?r()[c>>2]=D.getRenderbufferParameter(a,b):M||=1281},emscripten_glGetShaderInfoLog:(a,
b,c,e)=>{a=D.getShaderInfoLog(H[a]);null===a&&(a="(unknown error)");b=0<b&&e?C(a,e,b):0;c&&(r()[c>>2]=b)},emscripten_glGetShaderPrecisionFormat:(a,b,c,e)=>{a=D.getShaderPrecisionFormat(a,b);r()[c>>2]=a.rangeMin;r()[c+4>>2]=a.rangeMax;r()[e>>2]=a.precision},emscripten_glGetShaderiv:(a,b,c)=>{c?35716==b?(a=D.getShaderInfoLog(H[a]),null===a&&(a="(unknown error)"),a=a?a.length+1:0,r()[c>>2]=a):35720==b?(a=(a=D.getShaderSource(H[a]))?a.length+1:0,r()[c>>2]=a):r()[c>>2]=D.getShaderParameter(H[a],b):M||=
1281},emscripten_glGetString:sb,emscripten_glGetStringi:tb,emscripten_glGetUniformLocation:(a,b)=>{b=Ka(b);if(a=E[a]){var c=a,e=c.u,f=c.M,h;if(!e){c.u=e={};c.L={};var l=D.getProgramParameter(c,35718);for(h=0;h<l;++h){var m=D.getActiveUniform(c,h);var p=m.name;m=m.size;var v=ub(p);v=0<v?p.slice(0,v):p;var T=c.G;c.G+=m;f[v]=[m,T];for(p=0;p<m;++p)e[T]=p,c.L[T++]=v}}c=a.u;e=0;f=b;h=ub(b);0<h&&(e=parseInt(b.slice(h+1))>>>0,f=b.slice(0,h));if((f=a.M[f])&&e<f[0]&&(e+=f[1],c[e]=c[e]||D.getUniformLocation(a,
b)))return e}else M||=1281;return-1},emscripten_glInvalidateFramebuffer:(a,b,c)=>{for(var e=jb[b],f=0;f<b;f++)e[f]=r()[c+4*f>>2];D.invalidateFramebuffer(a,e)},emscripten_glInvalidateSubFramebuffer:(a,b,c,e,f,h,l)=>{for(var m=jb[b],p=0;p<b;p++)m[p]=r()[c+4*p>>2];D.invalidateSubFramebuffer(a,m,e,f,h,l)},emscripten_glIsSync:a=>D.isSync(K[a]),emscripten_glIsTexture:a=>(a=G[a])?D.isTexture(a):0,emscripten_glLineWidth:a=>D.lineWidth(a),emscripten_glLinkProgram:a=>{a=E[a];D.linkProgram(a);a.u=0;a.M={}},
emscripten_glMultiDrawArraysInstancedBaseInstanceWEBGL:(a,b,c,e,f,h)=>{D.K.multiDrawArraysInstancedBaseInstanceWEBGL(a,r(),b>>2,r(),c>>2,r(),e>>2,t(),f>>2,h)},emscripten_glMultiDrawElementsInstancedBaseVertexBaseInstanceWEBGL:(a,b,c,e,f,h,l,m)=>{D.K.multiDrawElementsInstancedBaseVertexBaseInstanceWEBGL(a,r(),b>>2,c,r(),e>>2,r(),f>>2,r(),h>>2,t(),l>>2,m)},emscripten_glPixelStorei:(a,b)=>{3317==a?bb=b:3314==a&&(cb=b);D.pixelStorei(a,b)},emscripten_glQueryCounterEXT:(a,b)=>{D.g.queryCounterEXT(I[a],
b)},emscripten_glReadBuffer:a=>D.readBuffer(a),emscripten_glReadPixels:(a,b,c,e,f,h,l)=>{if(2<=O.version)if(D.D)D.readPixels(a,b,c,e,f,h,l);else{var m=vb(h);l>>>=31-Math.clz32(m.BYTES_PER_ELEMENT);D.readPixels(a,b,c,e,f,h,m,l)}else(m=wb(h,f,c,e,l))?D.readPixels(a,b,c,e,f,h,m):M||=1280},emscripten_glRenderbufferStorage:(a,b,c,e)=>D.renderbufferStorage(a,b,c,e),emscripten_glRenderbufferStorageMultisample:(a,b,c,e,f)=>D.renderbufferStorageMultisample(a,b,c,e,f),emscripten_glSamplerParameterf:(a,b,c)=>
{D.samplerParameterf(J[a],b,c)},emscripten_glSamplerParameteri:(a,b,c)=>{D.samplerParameteri(J[a],b,c)},emscripten_glSamplerParameteriv:(a,b,c)=>{c=r()[c>>2];D.samplerParameteri(J[a],b,c)},emscripten_glScissor:(a,b,c,e)=>D.scissor(a,b,c,e),emscripten_glShaderSource:(a,b,c,e)=>{for(var f="",h=0;h<b;++h){var l=e?t()[e+4*h>>2]:void 0;f+=Ka(t()[c+4*h>>2],l)}D.shaderSource(H[a],f)},emscripten_glStencilFunc:(a,b,c)=>D.stencilFunc(a,b,c),emscripten_glStencilFuncSeparate:(a,b,c,e)=>D.stencilFuncSeparate(a,
b,c,e),emscripten_glStencilMask:a=>D.stencilMask(a),emscripten_glStencilMaskSeparate:(a,b)=>D.stencilMaskSeparate(a,b),emscripten_glStencilOp:(a,b,c)=>D.stencilOp(a,b,c),emscripten_glStencilOpSeparate:(a,b,c,e)=>D.stencilOpSeparate(a,b,c,e),emscripten_glTexImage2D:(a,b,c,e,f,h,l,m,p)=>{if(2<=O.version){if(D.o){D.texImage2D(a,b,c,e,f,h,l,m,p);return}if(p){var v=vb(m);p>>>=31-Math.clz32(v.BYTES_PER_ELEMENT);D.texImage2D(a,b,c,e,f,h,l,m,v,p);return}}v=p?wb(m,l,e,f,p):null;D.texImage2D(a,b,c,e,f,h,l,
m,v)},emscripten_glTexParameterf:(a,b,c)=>D.texParameterf(a,b,c),emscripten_glTexParameterfv:(a,b,c)=>{c=u()[c>>2];D.texParameterf(a,b,c)},emscripten_glTexParameteri:(a,b,c)=>D.texParameteri(a,b,c),emscripten_glTexParameteriv:(a,b,c)=>{c=r()[c>>2];D.texParameteri(a,b,c)},emscripten_glTexStorage2D:(a,b,c,e,f)=>D.texStorage2D(a,b,c,e,f),emscripten_glTexSubImage2D:(a,b,c,e,f,h,l,m,p)=>{if(2<=O.version){if(D.o){D.texSubImage2D(a,b,c,e,f,h,l,m,p);return}if(p){var v=vb(m);D.texSubImage2D(a,b,c,e,f,h,l,
m,v,p>>>31-Math.clz32(v.BYTES_PER_ELEMENT));return}}p=p?wb(m,l,f,h,p):null;D.texSubImage2D(a,b,c,e,f,h,l,m,p)},emscripten_glUniform1f:(a,b)=>{D.uniform1f(P(a),b)},emscripten_glUniform1fv:(a,b,c)=>{if(2<=O.version)b&&D.uniform1fv(P(a),u(),c>>2,b);else{if(288>=b)for(var e=Q[b],f=0;f<b;++f)e[f]=u()[c+4*f>>2];else e=u().subarray(c>>2,c+4*b>>2);D.uniform1fv(P(a),e)}},emscripten_glUniform1i:(a,b)=>{D.uniform1i(P(a),b)},emscripten_glUniform1iv:(a,b,c)=>{if(2<=O.version)b&&D.uniform1iv(P(a),r(),c>>2,b);else{if(288>=
b)for(var e=xb[b],f=0;f<b;++f)e[f]=r()[c+4*f>>2];else e=r().subarray(c>>2,c+4*b>>2);D.uniform1iv(P(a),e)}},emscripten_glUniform2f:(a,b,c)=>{D.uniform2f(P(a),b,c)},emscripten_glUniform2fv:(a,b,c)=>{if(2<=O.version)b&&D.uniform2fv(P(a),u(),c>>2,2*b);else{if(144>=b){b*=2;for(var e=Q[b],f=0;f<b;f+=2)e[f]=u()[c+4*f>>2],e[f+1]=u()[c+(4*f+4)>>2]}else e=u().subarray(c>>2,c+8*b>>2);D.uniform2fv(P(a),e)}},emscripten_glUniform2i:(a,b,c)=>{D.uniform2i(P(a),b,c)},emscripten_glUniform2iv:(a,b,c)=>{if(2<=O.version)b&&
D.uniform2iv(P(a),r(),c>>2,2*b);else{if(144>=b){b*=2;for(var e=xb[b],f=0;f<b;f+=2)e[f]=r()[c+4*f>>2],e[f+1]=r()[c+(4*f+4)>>2]}else e=r().subarray(c>>2,c+8*b>>2);D.uniform2iv(P(a),e)}},emscripten_glUniform3f:(a,b,c,e)=>{D.uniform3f(P(a),b,c,e)},emscripten_glUniform3fv:(a,b,c)=>{if(2<=O.version)b&&D.uniform3fv(P(a),u(),c>>2,3*b);else{if(96>=b){b*=3;for(var e=Q[b],f=0;f<b;f+=3)e[f]=u()[c+4*f>>2],e[f+1]=u()[c+(4*f+4)>>2],e[f+2]=u()[c+(4*f+8)>>2]}else e=u().subarray(c>>2,c+12*b>>2);D.uniform3fv(P(a),e)}},
emscripten_glUniform3i:(a,b,c,e)=>{D.uniform3i(P(a),b,c,e)},emscripten_glUniform3iv:(a,b,c)=>{if(2<=O.version)b&&D.uniform3iv(P(a),r(),c>>2,3*b);else{if(96>=b){b*=3;for(var e=xb[b],f=0;f<b;f+=3)e[f]=r()[c+4*f>>2],e[f+1]=r()[c+(4*f+4)>>2],e[f+2]=r()[c+(4*f+8)>>2]}else e=r().subarray(c>>2,c+12*b>>2);D.uniform3iv(P(a),e)}},emscripten_glUniform4f:(a,b,c,e,f)=>{D.uniform4f(P(a),b,c,e,f)},emscripten_glUniform4fv:(a,b,c)=>{if(2<=O.version)b&&D.uniform4fv(P(a),u(),c>>2,4*b);else{if(72>=b){var e=Q[4*b],f=
u();c>>=2;b*=4;for(var h=0;h<b;h+=4){var l=c+h;e[h]=f[l];e[h+1]=f[l+1];e[h+2]=f[l+2];e[h+3]=f[l+3]}}else e=u().subarray(c>>2,c+16*b>>2);D.uniform4fv(P(a),e)}},emscripten_glUniform4i:(a,b,c,e,f)=>{D.uniform4i(P(a),b,c,e,f)},emscripten_glUniform4iv:(a,b,c)=>{if(2<=O.version)b&&D.uniform4iv(P(a),r(),c>>2,4*b);else{if(72>=b){b*=4;for(var e=xb[b],f=0;f<b;f+=4)e[f]=r()[c+4*f>>2],e[f+1]=r()[c+(4*f+4)>>2],e[f+2]=r()[c+(4*f+8)>>2],e[f+3]=r()[c+(4*f+12)>>2]}else e=r().subarray(c>>2,c+16*b>>2);D.uniform4iv(P(a),
e)}},emscripten_glUniformMatrix2fv:(a,b,c,e)=>{if(2<=O.version)b&&D.uniformMatrix2fv(P(a),!!c,u(),e>>2,4*b);else{if(72>=b){b*=4;for(var f=Q[b],h=0;h<b;h+=4)f[h]=u()[e+4*h>>2],f[h+1]=u()[e+(4*h+4)>>2],f[h+2]=u()[e+(4*h+8)>>2],f[h+3]=u()[e+(4*h+12)>>2]}else f=u().subarray(e>>2,e+16*b>>2);D.uniformMatrix2fv(P(a),!!c,f)}},emscripten_glUniformMatrix3fv:(a,b,c,e)=>{if(2<=O.version)b&&D.uniformMatrix3fv(P(a),!!c,u(),e>>2,9*b);else{if(32>=b){b*=9;for(var f=Q[b],h=0;h<b;h+=9)f[h]=u()[e+4*h>>2],f[h+1]=u()[e+
(4*h+4)>>2],f[h+2]=u()[e+(4*h+8)>>2],f[h+3]=u()[e+(4*h+12)>>2],f[h+4]=u()[e+(4*h+16)>>2],f[h+5]=u()[e+(4*h+20)>>2],f[h+6]=u()[e+(4*h+24)>>2],f[h+7]=u()[e+(4*h+28)>>2],f[h+8]=u()[e+(4*h+32)>>2]}else f=u().subarray(e>>2,e+36*b>>2);D.uniformMatrix3fv(P(a),!!c,f)}},emscripten_glUniformMatrix4fv:(a,b,c,e)=>{if(2<=O.version)b&&D.uniformMatrix4fv(P(a),!!c,u(),e>>2,16*b);else{if(18>=b){var f=Q[16*b],h=u();e>>=2;b*=16;for(var l=0;l<b;l+=16){var m=e+l;f[l]=h[m];f[l+1]=h[m+1];f[l+2]=h[m+2];f[l+3]=h[m+3];f[l+
4]=h[m+4];f[l+5]=h[m+5];f[l+6]=h[m+6];f[l+7]=h[m+7];f[l+8]=h[m+8];f[l+9]=h[m+9];f[l+10]=h[m+10];f[l+11]=h[m+11];f[l+12]=h[m+12];f[l+13]=h[m+13];f[l+14]=h[m+14];f[l+15]=h[m+15]}}else f=u().subarray(e>>2,e+64*b>>2);D.uniformMatrix4fv(P(a),!!c,f)}},emscripten_glUseProgram:a=>{a=E[a];D.useProgram(a);D.N=a},emscripten_glVertexAttrib1f:(a,b)=>D.vertexAttrib1f(a,b),emscripten_glVertexAttrib2fv:(a,b)=>{D.vertexAttrib2f(a,u()[b>>2],u()[b+4>>2])},emscripten_glVertexAttrib3fv:(a,b)=>{D.vertexAttrib3f(a,u()[b>>
2],u()[b+4>>2],u()[b+8>>2])},emscripten_glVertexAttrib4fv:(a,b)=>{D.vertexAttrib4f(a,u()[b>>2],u()[b+4>>2],u()[b+8>>2],u()[b+12>>2])},emscripten_glVertexAttribDivisor:(a,b)=>{D.vertexAttribDivisor(a,b)},emscripten_glVertexAttribIPointer:(a,b,c,e,f)=>{D.vertexAttribIPointer(a,b,c,e,f)},emscripten_glVertexAttribPointer:(a,b,c,e,f,h)=>{D.vertexAttribPointer(a,b,c,!!e,f,h)},emscripten_glViewport:(a,b,c,e)=>D.viewport(a,b,c,e),emscripten_glWaitSync:(a,b,c,e)=>{D.waitSync(K[a],b,(c>>>0)+4294967296*e)},
emscripten_resize_heap:a=>{var b=q().length;a>>>=0;if(a<=b||2147483648<a)return!1;for(var c=1;4>=c;c*=2){var e=b*(1+.2/c);e=Math.min(e,a+100663296);a:{e=(Math.min(2147483648,65536*Math.ceil(Math.max(a,e)/65536))-g.buffer.byteLength+65535)/65536|0;try{g.grow(e);n();var f=1;break a}catch(h){}f=void 0}if(f)return!0}return!1},emscripten_webgl_enable_extension:function(a,b){a=Za[a];b=Ka(b);b.startsWith("GL_")&&(b=b.substr(3));"ANGLE_instanced_arrays"==b&&Pa(D);"OES_vertex_array_object"==b&&Qa(D);"WEBGL_draw_buffers"==
b&&Ra(D);"WEBGL_draw_instanced_base_vertex_base_instance"==b&&Sa(D);"WEBGL_multi_draw_instanced_base_vertex_base_instance"==b&&Ta(D);"WEBGL_multi_draw"==b&&(D.T=D.getExtension("WEBGL_multi_draw"));"EXT_polygon_offset_clamp"==b&&(D.P=D.getExtension("EXT_polygon_offset_clamp"));"EXT_clip_control"==b&&(D.O=D.getExtension("EXT_clip_control"));"WEBGL_polygon_mode"==b&&(D.Y=D.getExtension("WEBGL_polygon_mode"));return!!a.v.getExtension(b)},emscripten_webgl_get_current_context:()=>O?O.handle:0,emscripten_webgl_make_context_current:a=>
{O=Za[a];w.$=D=O?.v;return!a||D?0:-5},environ_get:(a,b)=>{var c=0;Ab().forEach((e,f)=>{var h=b+c;f=t()[a+4*f>>2]=h;for(h=0;h<e.length;++h)d()[f++]=e.charCodeAt(h);d()[f]=0;c+=e.length+1});return 0},environ_sizes_get:(a,b)=>{var c=Ab();t()[a>>2]=c.length;var e=0;c.forEach(f=>e+=f.length+1);t()[b>>2]=e;return 0},fd_close:()=>52,fd_pread:function(){return 52},fd_read:()=>52,fd_seek:function(){return 70},fd_write:(a,b,c,e)=>{for(var f=0,h=0;h<c;h++){var l=t()[b>>2],m=t()[b+4>>2];b+=8;for(var p=0;p<m;p++){var v=
q()[l+p],T=Bb[a];0===v||10===v?((1===a?na:y)(Ja(T)),T.length=0):T.push(v)}f+=m}t()[e>>2]=f;return 0},glDeleteTextures:hb,glGetIntegerv:ob,glGetString:sb,glGetStringi:tb,invoke_ii:bc,invoke_iii:cc,invoke_iiii:dc,invoke_iiiii:ec,invoke_iiiiiii:fc,invoke_vi:gc,invoke_vii:hc,invoke_viii:ic,invoke_viiii:jc,invoke_viiiiiii:kc,proc_exit:Na,skwasm_captureImageBitmap:Eb,skwasm_connectThread:Fb,skwasm_createGlTextureFromTextureSource:Gb,skwasm_createOffscreenCanvas:Hb,skwasm_dispatchDisposeSurface:Ib,skwasm_dispatchRasterizeImage:Jb,
skwasm_dispatchRenderPictures:Kb,skwasm_disposeAssociatedObjectOnThread:Lb,skwasm_getAssociatedObject:Mb,skwasm_postRasterizeResult:Nb,skwasm_resizeCanvas:Ob,skwasm_resolveAndPostImages:Pb,skwasm_setAssociatedObjectOnThread:Qb},W=function(){function a(c){W=c.exports;w.wasmExports=W;g=W.memory;w.wasmMemory=g;n();S=W.__indirect_function_table;ta.unshift(W.__wasm_call_ctors);z--;0==z&&(null!==va&&(clearInterval(va),va=null),A&&(c=A,A=null,c()));return W}var b={env:lc,wasi_snapshot_preview1:lc};z++;if(w.instantiateWasm)try{return w.instantiateWasm(b,
a)}catch(c){y(`Module.instantiateWasm callback failed with error: ${c}`),fa(c)}ya??=xa("skwasm_st.wasm")?"skwasm_st.wasm":x+"skwasm_st.wasm";Ba(b,function(c){a(c.instance,c.module)}).catch(fa);return{}}();w._canvas_saveLayer=(a,b,c,e,f)=>(w._canvas_saveLayer=W.canvas_saveLayer)(a,b,c,e,f);w._canvas_save=a=>(w._canvas_save=W.canvas_save)(a);w._canvas_restore=a=>(w._canvas_restore=W.canvas_restore)(a);w._canvas_restoreToCount=(a,b)=>(w._canvas_restoreToCount=W.canvas_restoreToCount)(a,b);
w._canvas_getSaveCount=a=>(w._canvas_getSaveCount=W.canvas_getSaveCount)(a);w._canvas_translate=(a,b,c)=>(w._canvas_translate=W.canvas_translate)(a,b,c);w._canvas_scale=(a,b,c)=>(w._canvas_scale=W.canvas_scale)(a,b,c);w._canvas_rotate=(a,b)=>(w._canvas_rotate=W.canvas_rotate)(a,b);w._canvas_skew=(a,b,c)=>(w._canvas_skew=W.canvas_skew)(a,b,c);w._canvas_transform=(a,b)=>(w._canvas_transform=W.canvas_transform)(a,b);w._canvas_clipRect=(a,b,c,e)=>(w._canvas_clipRect=W.canvas_clipRect)(a,b,c,e);
w._canvas_clipRRect=(a,b,c)=>(w._canvas_clipRRect=W.canvas_clipRRect)(a,b,c);w._canvas_clipPath=(a,b,c)=>(w._canvas_clipPath=W.canvas_clipPath)(a,b,c);w._canvas_drawColor=(a,b,c)=>(w._canvas_drawColor=W.canvas_drawColor)(a,b,c);w._canvas_drawLine=(a,b,c,e,f,h)=>(w._canvas_drawLine=W.canvas_drawLine)(a,b,c,e,f,h);w._canvas_drawPaint=(a,b)=>(w._canvas_drawPaint=W.canvas_drawPaint)(a,b);w._canvas_drawRect=(a,b,c)=>(w._canvas_drawRect=W.canvas_drawRect)(a,b,c);
w._canvas_drawRRect=(a,b,c)=>(w._canvas_drawRRect=W.canvas_drawRRect)(a,b,c);w._canvas_drawDRRect=(a,b,c,e)=>(w._canvas_drawDRRect=W.canvas_drawDRRect)(a,b,c,e);w._canvas_drawOval=(a,b,c)=>(w._canvas_drawOval=W.canvas_drawOval)(a,b,c);w._canvas_drawCircle=(a,b,c,e,f)=>(w._canvas_drawCircle=W.canvas_drawCircle)(a,b,c,e,f);w._canvas_drawArc=(a,b,c,e,f,h)=>(w._canvas_drawArc=W.canvas_drawArc)(a,b,c,e,f,h);w._canvas_drawPath=(a,b,c)=>(w._canvas_drawPath=W.canvas_drawPath)(a,b,c);
w._canvas_drawShadow=(a,b,c,e,f,h)=>(w._canvas_drawShadow=W.canvas_drawShadow)(a,b,c,e,f,h);w._canvas_drawParagraph=(a,b,c,e)=>(w._canvas_drawParagraph=W.canvas_drawParagraph)(a,b,c,e);w._canvas_drawPicture=(a,b)=>(w._canvas_drawPicture=W.canvas_drawPicture)(a,b);w._canvas_drawImage=(a,b,c,e,f,h)=>(w._canvas_drawImage=W.canvas_drawImage)(a,b,c,e,f,h);w._canvas_drawImageRect=(a,b,c,e,f,h)=>(w._canvas_drawImageRect=W.canvas_drawImageRect)(a,b,c,e,f,h);
w._canvas_drawImageNine=(a,b,c,e,f,h)=>(w._canvas_drawImageNine=W.canvas_drawImageNine)(a,b,c,e,f,h);w._canvas_drawVertices=(a,b,c,e)=>(w._canvas_drawVertices=W.canvas_drawVertices)(a,b,c,e);w._canvas_drawPoints=(a,b,c,e,f)=>(w._canvas_drawPoints=W.canvas_drawPoints)(a,b,c,e,f);w._canvas_drawAtlas=(a,b,c,e,f,h,l,m,p)=>(w._canvas_drawAtlas=W.canvas_drawAtlas)(a,b,c,e,f,h,l,m,p);w._canvas_getTransform=(a,b)=>(w._canvas_getTransform=W.canvas_getTransform)(a,b);
w._canvas_getLocalClipBounds=(a,b)=>(w._canvas_getLocalClipBounds=W.canvas_getLocalClipBounds)(a,b);w._canvas_getDeviceClipBounds=(a,b)=>(w._canvas_getDeviceClipBounds=W.canvas_getDeviceClipBounds)(a,b);w._contourMeasureIter_create=(a,b,c)=>(w._contourMeasureIter_create=W.contourMeasureIter_create)(a,b,c);w._contourMeasureIter_next=a=>(w._contourMeasureIter_next=W.contourMeasureIter_next)(a);w._contourMeasureIter_dispose=a=>(w._contourMeasureIter_dispose=W.contourMeasureIter_dispose)(a);
w._contourMeasure_dispose=a=>(w._contourMeasure_dispose=W.contourMeasure_dispose)(a);w._contourMeasure_length=a=>(w._contourMeasure_length=W.contourMeasure_length)(a);w._contourMeasure_isClosed=a=>(w._contourMeasure_isClosed=W.contourMeasure_isClosed)(a);w._contourMeasure_getPosTan=(a,b,c,e)=>(w._contourMeasure_getPosTan=W.contourMeasure_getPosTan)(a,b,c,e);w._contourMeasure_getSegment=(a,b,c,e)=>(w._contourMeasure_getSegment=W.contourMeasure_getSegment)(a,b,c,e);
w._skData_create=a=>(w._skData_create=W.skData_create)(a);w._skData_getPointer=a=>(w._skData_getPointer=W.skData_getPointer)(a);w._skData_getConstPointer=a=>(w._skData_getConstPointer=W.skData_getConstPointer)(a);w._skData_getSize=a=>(w._skData_getSize=W.skData_getSize)(a);w._skData_dispose=a=>(w._skData_dispose=W.skData_dispose)(a);w._imageFilter_createBlur=(a,b,c)=>(w._imageFilter_createBlur=W.imageFilter_createBlur)(a,b,c);
w._imageFilter_createDilate=(a,b)=>(w._imageFilter_createDilate=W.imageFilter_createDilate)(a,b);w._imageFilter_createErode=(a,b)=>(w._imageFilter_createErode=W.imageFilter_createErode)(a,b);w._imageFilter_createMatrix=(a,b)=>(w._imageFilter_createMatrix=W.imageFilter_createMatrix)(a,b);w._imageFilter_createFromColorFilter=a=>(w._imageFilter_createFromColorFilter=W.imageFilter_createFromColorFilter)(a);w._imageFilter_compose=(a,b)=>(w._imageFilter_compose=W.imageFilter_compose)(a,b);
w._imageFilter_dispose=a=>(w._imageFilter_dispose=W.imageFilter_dispose)(a);w._imageFilter_getFilterBounds=(a,b)=>(w._imageFilter_getFilterBounds=W.imageFilter_getFilterBounds)(a,b);w._colorFilter_createMode=(a,b)=>(w._colorFilter_createMode=W.colorFilter_createMode)(a,b);w._colorFilter_createMatrix=a=>(w._colorFilter_createMatrix=W.colorFilter_createMatrix)(a);w._colorFilter_createSRGBToLinearGamma=()=>(w._colorFilter_createSRGBToLinearGamma=W.colorFilter_createSRGBToLinearGamma)();
w._colorFilter_createLinearToSRGBGamma=()=>(w._colorFilter_createLinearToSRGBGamma=W.colorFilter_createLinearToSRGBGamma)();w._colorFilter_compose=(a,b)=>(w._colorFilter_compose=W.colorFilter_compose)(a,b);w._colorFilter_dispose=a=>(w._colorFilter_dispose=W.colorFilter_dispose)(a);w._maskFilter_createBlur=(a,b)=>(w._maskFilter_createBlur=W.maskFilter_createBlur)(a,b);w._maskFilter_dispose=a=>(w._maskFilter_dispose=W.maskFilter_dispose)(a);w._fontCollection_create=()=>(w._fontCollection_create=W.fontCollection_create)();
w._fontCollection_dispose=a=>(w._fontCollection_dispose=W.fontCollection_dispose)(a);w._typeface_create=a=>(w._typeface_create=W.typeface_create)(a);w._typeface_dispose=a=>(w._typeface_dispose=W.typeface_dispose)(a);w._typefaces_filterCoveredCodePoints=(a,b,c,e)=>(w._typefaces_filterCoveredCodePoints=W.typefaces_filterCoveredCodePoints)(a,b,c,e);w._fontCollection_registerTypeface=(a,b,c)=>(w._fontCollection_registerTypeface=W.fontCollection_registerTypeface)(a,b,c);
w._fontCollection_clearCaches=a=>(w._fontCollection_clearCaches=W.fontCollection_clearCaches)(a);w._image_createFromPicture=(a,b,c)=>(w._image_createFromPicture=W.image_createFromPicture)(a,b,c);w._image_createFromPixels=(a,b,c,e,f)=>(w._image_createFromPixels=W.image_createFromPixels)(a,b,c,e,f);w._image_createFromTextureSource=(a,b,c,e)=>(w._image_createFromTextureSource=W.image_createFromTextureSource)(a,b,c,e);w._image_ref=a=>(w._image_ref=W.image_ref)(a);
w._image_dispose=a=>(w._image_dispose=W.image_dispose)(a);w._image_getWidth=a=>(w._image_getWidth=W.image_getWidth)(a);w._image_getHeight=a=>(w._image_getHeight=W.image_getHeight)(a);w._paint_create=(a,b,c,e,f,h,l,m)=>(w._paint_create=W.paint_create)(a,b,c,e,f,h,l,m);w._paint_dispose=a=>(w._paint_dispose=W.paint_dispose)(a);w._paint_setShader=(a,b)=>(w._paint_setShader=W.paint_setShader)(a,b);w._paint_setImageFilter=(a,b)=>(w._paint_setImageFilter=W.paint_setImageFilter)(a,b);
w._paint_setColorFilter=(a,b)=>(w._paint_setColorFilter=W.paint_setColorFilter)(a,b);w._paint_setMaskFilter=(a,b)=>(w._paint_setMaskFilter=W.paint_setMaskFilter)(a,b);w._path_create=()=>(w._path_create=W.path_create)();w._path_dispose=a=>(w._path_dispose=W.path_dispose)(a);w._path_copy=a=>(w._path_copy=W.path_copy)(a);w._path_setFillType=(a,b)=>(w._path_setFillType=W.path_setFillType)(a,b);w._path_getFillType=a=>(w._path_getFillType=W.path_getFillType)(a);
w._path_moveTo=(a,b,c)=>(w._path_moveTo=W.path_moveTo)(a,b,c);w._path_relativeMoveTo=(a,b,c)=>(w._path_relativeMoveTo=W.path_relativeMoveTo)(a,b,c);w._path_lineTo=(a,b,c)=>(w._path_lineTo=W.path_lineTo)(a,b,c);w._path_relativeLineTo=(a,b,c)=>(w._path_relativeLineTo=W.path_relativeLineTo)(a,b,c);w._path_quadraticBezierTo=(a,b,c,e,f)=>(w._path_quadraticBezierTo=W.path_quadraticBezierTo)(a,b,c,e,f);
w._path_relativeQuadraticBezierTo=(a,b,c,e,f)=>(w._path_relativeQuadraticBezierTo=W.path_relativeQuadraticBezierTo)(a,b,c,e,f);w._path_cubicTo=(a,b,c,e,f,h,l)=>(w._path_cubicTo=W.path_cubicTo)(a,b,c,e,f,h,l);w._path_relativeCubicTo=(a,b,c,e,f,h,l)=>(w._path_relativeCubicTo=W.path_relativeCubicTo)(a,b,c,e,f,h,l);w._path_conicTo=(a,b,c,e,f,h)=>(w._path_conicTo=W.path_conicTo)(a,b,c,e,f,h);w._path_relativeConicTo=(a,b,c,e,f,h)=>(w._path_relativeConicTo=W.path_relativeConicTo)(a,b,c,e,f,h);
w._path_arcToOval=(a,b,c,e,f)=>(w._path_arcToOval=W.path_arcToOval)(a,b,c,e,f);w._path_arcToRotated=(a,b,c,e,f,h,l,m)=>(w._path_arcToRotated=W.path_arcToRotated)(a,b,c,e,f,h,l,m);w._path_relativeArcToRotated=(a,b,c,e,f,h,l,m)=>(w._path_relativeArcToRotated=W.path_relativeArcToRotated)(a,b,c,e,f,h,l,m);w._path_addRect=(a,b)=>(w._path_addRect=W.path_addRect)(a,b);w._path_addOval=(a,b)=>(w._path_addOval=W.path_addOval)(a,b);w._path_addArc=(a,b,c,e)=>(w._path_addArc=W.path_addArc)(a,b,c,e);
w._path_addPolygon=(a,b,c,e)=>(w._path_addPolygon=W.path_addPolygon)(a,b,c,e);w._path_addRRect=(a,b)=>(w._path_addRRect=W.path_addRRect)(a,b);w._path_addPath=(a,b,c,e)=>(w._path_addPath=W.path_addPath)(a,b,c,e);w._path_close=a=>(w._path_close=W.path_close)(a);w._path_reset=a=>(w._path_reset=W.path_reset)(a);w._path_contains=(a,b,c)=>(w._path_contains=W.path_contains)(a,b,c);w._path_transform=(a,b)=>(w._path_transform=W.path_transform)(a,b);
w._path_getBounds=(a,b)=>(w._path_getBounds=W.path_getBounds)(a,b);w._path_combine=(a,b,c)=>(w._path_combine=W.path_combine)(a,b,c);w._path_getSvgString=a=>(w._path_getSvgString=W.path_getSvgString)(a);w._pictureRecorder_create=()=>(w._pictureRecorder_create=W.pictureRecorder_create)();w._pictureRecorder_dispose=a=>(w._pictureRecorder_dispose=W.pictureRecorder_dispose)(a);w._pictureRecorder_beginRecording=(a,b)=>(w._pictureRecorder_beginRecording=W.pictureRecorder_beginRecording)(a,b);
w._pictureRecorder_endRecording=a=>(w._pictureRecorder_endRecording=W.pictureRecorder_endRecording)(a);w._picture_getCullRect=(a,b)=>(w._picture_getCullRect=W.picture_getCullRect)(a,b);w._picture_dispose=a=>(w._picture_dispose=W.picture_dispose)(a);w._picture_approximateBytesUsed=a=>(w._picture_approximateBytesUsed=W.picture_approximateBytesUsed)(a);w._shader_createLinearGradient=(a,b,c,e,f,h)=>(w._shader_createLinearGradient=W.shader_createLinearGradient)(a,b,c,e,f,h);
w._shader_createRadialGradient=(a,b,c,e,f,h,l,m)=>(w._shader_createRadialGradient=W.shader_createRadialGradient)(a,b,c,e,f,h,l,m);w._shader_createConicalGradient=(a,b,c,e,f,h,l,m)=>(w._shader_createConicalGradient=W.shader_createConicalGradient)(a,b,c,e,f,h,l,m);w._shader_createSweepGradient=(a,b,c,e,f,h,l,m,p)=>(w._shader_createSweepGradient=W.shader_createSweepGradient)(a,b,c,e,f,h,l,m,p);w._shader_dispose=a=>(w._shader_dispose=W.shader_dispose)(a);
w._runtimeEffect_create=a=>(w._runtimeEffect_create=W.runtimeEffect_create)(a);w._runtimeEffect_dispose=a=>(w._runtimeEffect_dispose=W.runtimeEffect_dispose)(a);w._runtimeEffect_getUniformSize=a=>(w._runtimeEffect_getUniformSize=W.runtimeEffect_getUniformSize)(a);w._shader_createRuntimeEffectShader=(a,b,c,e)=>(w._shader_createRuntimeEffectShader=W.shader_createRuntimeEffectShader)(a,b,c,e);w._shader_createFromImage=(a,b,c,e,f)=>(w._shader_createFromImage=W.shader_createFromImage)(a,b,c,e,f);
w._skString_allocate=a=>(w._skString_allocate=W.skString_allocate)(a);w._skString_getData=a=>(w._skString_getData=W.skString_getData)(a);w._skString_getLength=a=>(w._skString_getLength=W.skString_getLength)(a);w._skString_free=a=>(w._skString_free=W.skString_free)(a);w._skString16_allocate=a=>(w._skString16_allocate=W.skString16_allocate)(a);w._skString16_getData=a=>(w._skString16_getData=W.skString16_getData)(a);w._skString16_free=a=>(w._skString16_free=W.skString16_free)(a);
w._surface_create=()=>(w._surface_create=W.surface_create)();w._surface_getThreadId=a=>(w._surface_getThreadId=W.surface_getThreadId)(a);w._surface_setCallbackHandler=(a,b)=>(w._surface_setCallbackHandler=W.surface_setCallbackHandler)(a,b);w._surface_destroy=a=>(w._surface_destroy=W.surface_destroy)(a);var Yb=w._surface_dispose=a=>(Yb=w._surface_dispose=W.surface_dispose)(a);w._surface_renderPictures=(a,b,c)=>(w._surface_renderPictures=W.surface_renderPictures)(a,b,c);
var Wb=w._surface_renderPicturesOnWorker=(a,b,c,e,f)=>(Wb=w._surface_renderPicturesOnWorker=W.surface_renderPicturesOnWorker)(a,b,c,e,f);w._surface_rasterizeImage=(a,b,c)=>(w._surface_rasterizeImage=W.surface_rasterizeImage)(a,b,c);
var Zb=w._surface_rasterizeImageOnWorker=(a,b,c,e)=>(Zb=w._surface_rasterizeImageOnWorker=W.surface_rasterizeImageOnWorker)(a,b,c,e),Xb=w._surface_onRenderComplete=(a,b,c)=>(Xb=w._surface_onRenderComplete=W.surface_onRenderComplete)(a,b,c),$b=w._surface_onRasterizeComplete=(a,b,c)=>($b=w._surface_onRasterizeComplete=W.surface_onRasterizeComplete)(a,b,c);w._lineMetrics_create=(a,b,c,e,f,h,l,m,p)=>(w._lineMetrics_create=W.lineMetrics_create)(a,b,c,e,f,h,l,m,p);
w._lineMetrics_dispose=a=>(w._lineMetrics_dispose=W.lineMetrics_dispose)(a);w._lineMetrics_getHardBreak=a=>(w._lineMetrics_getHardBreak=W.lineMetrics_getHardBreak)(a);w._lineMetrics_getAscent=a=>(w._lineMetrics_getAscent=W.lineMetrics_getAscent)(a);w._lineMetrics_getDescent=a=>(w._lineMetrics_getDescent=W.lineMetrics_getDescent)(a);w._lineMetrics_getUnscaledAscent=a=>(w._lineMetrics_getUnscaledAscent=W.lineMetrics_getUnscaledAscent)(a);w._lineMetrics_getHeight=a=>(w._lineMetrics_getHeight=W.lineMetrics_getHeight)(a);
w._lineMetrics_getWidth=a=>(w._lineMetrics_getWidth=W.lineMetrics_getWidth)(a);w._lineMetrics_getLeft=a=>(w._lineMetrics_getLeft=W.lineMetrics_getLeft)(a);w._lineMetrics_getBaseline=a=>(w._lineMetrics_getBaseline=W.lineMetrics_getBaseline)(a);w._lineMetrics_getLineNumber=a=>(w._lineMetrics_getLineNumber=W.lineMetrics_getLineNumber)(a);w._lineMetrics_getStartIndex=a=>(w._lineMetrics_getStartIndex=W.lineMetrics_getStartIndex)(a);w._lineMetrics_getEndIndex=a=>(w._lineMetrics_getEndIndex=W.lineMetrics_getEndIndex)(a);
w._paragraph_dispose=a=>(w._paragraph_dispose=W.paragraph_dispose)(a);w._paragraph_getWidth=a=>(w._paragraph_getWidth=W.paragraph_getWidth)(a);w._paragraph_getHeight=a=>(w._paragraph_getHeight=W.paragraph_getHeight)(a);w._paragraph_getLongestLine=a=>(w._paragraph_getLongestLine=W.paragraph_getLongestLine)(a);w._paragraph_getMinIntrinsicWidth=a=>(w._paragraph_getMinIntrinsicWidth=W.paragraph_getMinIntrinsicWidth)(a);w._paragraph_getMaxIntrinsicWidth=a=>(w._paragraph_getMaxIntrinsicWidth=W.paragraph_getMaxIntrinsicWidth)(a);
w._paragraph_getAlphabeticBaseline=a=>(w._paragraph_getAlphabeticBaseline=W.paragraph_getAlphabeticBaseline)(a);w._paragraph_getIdeographicBaseline=a=>(w._paragraph_getIdeographicBaseline=W.paragraph_getIdeographicBaseline)(a);w._paragraph_getDidExceedMaxLines=a=>(w._paragraph_getDidExceedMaxLines=W.paragraph_getDidExceedMaxLines)(a);w._paragraph_layout=(a,b)=>(w._paragraph_layout=W.paragraph_layout)(a,b);
w._paragraph_getPositionForOffset=(a,b,c,e)=>(w._paragraph_getPositionForOffset=W.paragraph_getPositionForOffset)(a,b,c,e);w._paragraph_getClosestGlyphInfoAtCoordinate=(a,b,c,e,f,h)=>(w._paragraph_getClosestGlyphInfoAtCoordinate=W.paragraph_getClosestGlyphInfoAtCoordinate)(a,b,c,e,f,h);w._paragraph_getGlyphInfoAt=(a,b,c,e,f)=>(w._paragraph_getGlyphInfoAt=W.paragraph_getGlyphInfoAt)(a,b,c,e,f);w._paragraph_getWordBoundary=(a,b,c)=>(w._paragraph_getWordBoundary=W.paragraph_getWordBoundary)(a,b,c);
w._paragraph_getLineCount=a=>(w._paragraph_getLineCount=W.paragraph_getLineCount)(a);w._paragraph_getLineNumberAt=(a,b)=>(w._paragraph_getLineNumberAt=W.paragraph_getLineNumberAt)(a,b);w._paragraph_getLineMetricsAtIndex=(a,b)=>(w._paragraph_getLineMetricsAtIndex=W.paragraph_getLineMetricsAtIndex)(a,b);w._textBoxList_dispose=a=>(w._textBoxList_dispose=W.textBoxList_dispose)(a);w._textBoxList_getLength=a=>(w._textBoxList_getLength=W.textBoxList_getLength)(a);
w._textBoxList_getBoxAtIndex=(a,b,c)=>(w._textBoxList_getBoxAtIndex=W.textBoxList_getBoxAtIndex)(a,b,c);w._paragraph_getBoxesForRange=(a,b,c,e,f)=>(w._paragraph_getBoxesForRange=W.paragraph_getBoxesForRange)(a,b,c,e,f);w._paragraph_getBoxesForPlaceholders=a=>(w._paragraph_getBoxesForPlaceholders=W.paragraph_getBoxesForPlaceholders)(a);w._paragraph_getUnresolvedCodePoints=(a,b,c)=>(w._paragraph_getUnresolvedCodePoints=W.paragraph_getUnresolvedCodePoints)(a,b,c);
w._paragraphBuilder_create=(a,b)=>(w._paragraphBuilder_create=W.paragraphBuilder_create)(a,b);w._paragraphBuilder_dispose=a=>(w._paragraphBuilder_dispose=W.paragraphBuilder_dispose)(a);w._paragraphBuilder_addPlaceholder=(a,b,c,e,f,h)=>(w._paragraphBuilder_addPlaceholder=W.paragraphBuilder_addPlaceholder)(a,b,c,e,f,h);w._paragraphBuilder_addText=(a,b)=>(w._paragraphBuilder_addText=W.paragraphBuilder_addText)(a,b);
w._paragraphBuilder_getUtf8Text=(a,b)=>(w._paragraphBuilder_getUtf8Text=W.paragraphBuilder_getUtf8Text)(a,b);w._paragraphBuilder_pushStyle=(a,b)=>(w._paragraphBuilder_pushStyle=W.paragraphBuilder_pushStyle)(a,b);w._paragraphBuilder_pop=a=>(w._paragraphBuilder_pop=W.paragraphBuilder_pop)(a);w._paragraphBuilder_build=a=>(w._paragraphBuilder_build=W.paragraphBuilder_build)(a);w._unicodePositionBuffer_create=a=>(w._unicodePositionBuffer_create=W.unicodePositionBuffer_create)(a);
w._unicodePositionBuffer_getDataPointer=a=>(w._unicodePositionBuffer_getDataPointer=W.unicodePositionBuffer_getDataPointer)(a);w._unicodePositionBuffer_free=a=>(w._unicodePositionBuffer_free=W.unicodePositionBuffer_free)(a);w._lineBreakBuffer_create=a=>(w._lineBreakBuffer_create=W.lineBreakBuffer_create)(a);w._lineBreakBuffer_getDataPointer=a=>(w._lineBreakBuffer_getDataPointer=W.lineBreakBuffer_getDataPointer)(a);w._lineBreakBuffer_free=a=>(w._lineBreakBuffer_free=W.lineBreakBuffer_free)(a);
w._paragraphBuilder_setGraphemeBreaksUtf16=(a,b)=>(w._paragraphBuilder_setGraphemeBreaksUtf16=W.paragraphBuilder_setGraphemeBreaksUtf16)(a,b);w._paragraphBuilder_setWordBreaksUtf16=(a,b)=>(w._paragraphBuilder_setWordBreaksUtf16=W.paragraphBuilder_setWordBreaksUtf16)(a,b);w._paragraphBuilder_setLineBreaksUtf16=(a,b)=>(w._paragraphBuilder_setLineBreaksUtf16=W.paragraphBuilder_setLineBreaksUtf16)(a,b);w._paragraphStyle_create=()=>(w._paragraphStyle_create=W.paragraphStyle_create)();
w._paragraphStyle_dispose=a=>(w._paragraphStyle_dispose=W.paragraphStyle_dispose)(a);w._paragraphStyle_setTextAlign=(a,b)=>(w._paragraphStyle_setTextAlign=W.paragraphStyle_setTextAlign)(a,b);w._paragraphStyle_setTextDirection=(a,b)=>(w._paragraphStyle_setTextDirection=W.paragraphStyle_setTextDirection)(a,b);w._paragraphStyle_setMaxLines=(a,b)=>(w._paragraphStyle_setMaxLines=W.paragraphStyle_setMaxLines)(a,b);
w._paragraphStyle_setHeight=(a,b)=>(w._paragraphStyle_setHeight=W.paragraphStyle_setHeight)(a,b);w._paragraphStyle_setTextHeightBehavior=(a,b,c)=>(w._paragraphStyle_setTextHeightBehavior=W.paragraphStyle_setTextHeightBehavior)(a,b,c);w._paragraphStyle_setEllipsis=(a,b)=>(w._paragraphStyle_setEllipsis=W.paragraphStyle_setEllipsis)(a,b);w._paragraphStyle_setStrutStyle=(a,b)=>(w._paragraphStyle_setStrutStyle=W.paragraphStyle_setStrutStyle)(a,b);
w._paragraphStyle_setTextStyle=(a,b)=>(w._paragraphStyle_setTextStyle=W.paragraphStyle_setTextStyle)(a,b);w._paragraphStyle_setApplyRoundingHack=(a,b)=>(w._paragraphStyle_setApplyRoundingHack=W.paragraphStyle_setApplyRoundingHack)(a,b);w._strutStyle_create=()=>(w._strutStyle_create=W.strutStyle_create)();w._strutStyle_dispose=a=>(w._strutStyle_dispose=W.strutStyle_dispose)(a);w._strutStyle_setFontFamilies=(a,b,c)=>(w._strutStyle_setFontFamilies=W.strutStyle_setFontFamilies)(a,b,c);
w._strutStyle_setFontSize=(a,b)=>(w._strutStyle_setFontSize=W.strutStyle_setFontSize)(a,b);w._strutStyle_setHeight=(a,b)=>(w._strutStyle_setHeight=W.strutStyle_setHeight)(a,b);w._strutStyle_setHalfLeading=(a,b)=>(w._strutStyle_setHalfLeading=W.strutStyle_setHalfLeading)(a,b);w._strutStyle_setLeading=(a,b)=>(w._strutStyle_setLeading=W.strutStyle_setLeading)(a,b);w._strutStyle_setFontStyle=(a,b,c)=>(w._strutStyle_setFontStyle=W.strutStyle_setFontStyle)(a,b,c);
w._strutStyle_setForceStrutHeight=(a,b)=>(w._strutStyle_setForceStrutHeight=W.strutStyle_setForceStrutHeight)(a,b);w._textStyle_create=()=>(w._textStyle_create=W.textStyle_create)();w._textStyle_copy=a=>(w._textStyle_copy=W.textStyle_copy)(a);w._textStyle_dispose=a=>(w._textStyle_dispose=W.textStyle_dispose)(a);w._textStyle_setColor=(a,b)=>(w._textStyle_setColor=W.textStyle_setColor)(a,b);w._textStyle_setDecoration=(a,b)=>(w._textStyle_setDecoration=W.textStyle_setDecoration)(a,b);
w._textStyle_setDecorationColor=(a,b)=>(w._textStyle_setDecorationColor=W.textStyle_setDecorationColor)(a,b);w._textStyle_setDecorationStyle=(a,b)=>(w._textStyle_setDecorationStyle=W.textStyle_setDecorationStyle)(a,b);w._textStyle_setDecorationThickness=(a,b)=>(w._textStyle_setDecorationThickness=W.textStyle_setDecorationThickness)(a,b);w._textStyle_setFontStyle=(a,b,c)=>(w._textStyle_setFontStyle=W.textStyle_setFontStyle)(a,b,c);
w._textStyle_setTextBaseline=(a,b)=>(w._textStyle_setTextBaseline=W.textStyle_setTextBaseline)(a,b);w._textStyle_clearFontFamilies=a=>(w._textStyle_clearFontFamilies=W.textStyle_clearFontFamilies)(a);w._textStyle_addFontFamilies=(a,b,c)=>(w._textStyle_addFontFamilies=W.textStyle_addFontFamilies)(a,b,c);w._textStyle_setFontSize=(a,b)=>(w._textStyle_setFontSize=W.textStyle_setFontSize)(a,b);w._textStyle_setLetterSpacing=(a,b)=>(w._textStyle_setLetterSpacing=W.textStyle_setLetterSpacing)(a,b);
w._textStyle_setWordSpacing=(a,b)=>(w._textStyle_setWordSpacing=W.textStyle_setWordSpacing)(a,b);w._textStyle_setHeight=(a,b)=>(w._textStyle_setHeight=W.textStyle_setHeight)(a,b);w._textStyle_setHalfLeading=(a,b)=>(w._textStyle_setHalfLeading=W.textStyle_setHalfLeading)(a,b);w._textStyle_setLocale=(a,b)=>(w._textStyle_setLocale=W.textStyle_setLocale)(a,b);w._textStyle_setBackground=(a,b)=>(w._textStyle_setBackground=W.textStyle_setBackground)(a,b);
w._textStyle_setForeground=(a,b)=>(w._textStyle_setForeground=W.textStyle_setForeground)(a,b);w._textStyle_addShadow=(a,b,c,e,f)=>(w._textStyle_addShadow=W.textStyle_addShadow)(a,b,c,e,f);w._textStyle_addFontFeature=(a,b,c)=>(w._textStyle_addFontFeature=W.textStyle_addFontFeature)(a,b,c);w._textStyle_setFontVariations=(a,b,c,e)=>(w._textStyle_setFontVariations=W.textStyle_setFontVariations)(a,b,c,e);w._vertices_create=(a,b,c,e,f,h,l)=>(w._vertices_create=W.vertices_create)(a,b,c,e,f,h,l);
w._vertices_dispose=a=>(w._vertices_dispose=W.vertices_dispose)(a);w._skwasm_isMultiThreaded=()=>(w._skwasm_isMultiThreaded=W.skwasm_isMultiThreaded)();var qb=a=>(qb=W.malloc)(a),ac=(a,b)=>(ac=W._emscripten_timeout)(a,b),X=(a,b)=>(X=W.setThrew)(a,b),Y=a=>(Y=W._emscripten_stack_restore)(a),Sb=a=>(Sb=W._emscripten_stack_alloc)(a),Z=()=>(Z=W.emscripten_stack_get_current)();function cc(a,b,c){var e=Z();try{return S.get(a)(b,c)}catch(f){Y(e);if(f!==f+0)throw f;X(1,0)}}
function hc(a,b,c){var e=Z();try{S.get(a)(b,c)}catch(f){Y(e);if(f!==f+0)throw f;X(1,0)}}function bc(a,b){var c=Z();try{return S.get(a)(b)}catch(e){Y(c);if(e!==e+0)throw e;X(1,0)}}function ic(a,b,c,e){var f=Z();try{S.get(a)(b,c,e)}catch(h){Y(f);if(h!==h+0)throw h;X(1,0)}}function dc(a,b,c,e){var f=Z();try{return S.get(a)(b,c,e)}catch(h){Y(f);if(h!==h+0)throw h;X(1,0)}}function jc(a,b,c,e,f){var h=Z();try{S.get(a)(b,c,e,f)}catch(l){Y(h);if(l!==l+0)throw l;X(1,0)}}
function kc(a,b,c,e,f,h,l,m){var p=Z();try{S.get(a)(b,c,e,f,h,l,m)}catch(v){Y(p);if(v!==v+0)throw v;X(1,0)}}function gc(a,b){var c=Z();try{S.get(a)(b)}catch(e){Y(c);if(e!==e+0)throw e;X(1,0)}}function fc(a,b,c,e,f,h,l){var m=Z();try{return S.get(a)(b,c,e,f,h,l)}catch(p){Y(m);if(p!==p+0)throw p;X(1,0)}}function ec(a,b,c,e,f){var h=Z();try{return S.get(a)(b,c,e,f)}catch(l){Y(h);if(l!==l+0)throw l;X(1,0)}}w.wasmMemory=g;w.wasmExports=W;w.stackAlloc=Tb;
w.addFunction=(a,b)=>{if(!U){U=new WeakMap;var c=S.length;if(U)for(var e=0;e<0+c;e++){var f=S.get(e);f&&U.set(f,e)}}if(c=U.get(a)||0)return c;if(Rb.length)c=Rb.pop();else{try{S.grow(1)}catch(m){if(!(m instanceof RangeError))throw m;throw"Unable to grow wasm table. Set ALLOW_TABLE_GROWTH.";}c=S.length-1}try{S.set(c,a)}catch(m){if(!(m instanceof TypeError))throw m;if("function"==typeof WebAssembly.Function){e=WebAssembly.Function;f={i:"i32",j:"i64",f:"f32",d:"f64",e:"externref",p:"i32"};for(var h={parameters:[],
results:"v"==b[0]?[]:[f[b[0]]]},l=1;l<b.length;++l)h.parameters.push(f[b[l]]);b=new e(h,a)}else{e=[1];f=b.slice(0,1);b=b.slice(1);h={i:127,p:127,j:126,f:125,d:124,e:111};e.push(96);l=b.length;128>l?e.push(l):e.push(l%128|128,l>>7);for(l=0;l<b.length;++l)e.push(h[b[l]]);"v"==f?e.push(0):e.push(1,h[f]);b=[0,97,115,109,1,0,0,0,1];f=e.length;128>f?b.push(f):b.push(f%128|128,f>>7);b.push(...e);b.push(2,7,1,1,101,1,102,0,0,7,5,1,1,102,0,0);b=new WebAssembly.Module(new Uint8Array(b));b=(new WebAssembly.Instance(b,
{e:{f:a}})).exports.f}S.set(c,b)}U.set(a,c);return c};var mc,nc;A=function oc(){mc||pc();mc||(A=oc)};function pc(){if(!(0<z)){if(!nc&&(nc=1,Da(sa),0<z))return;mc||(mc=1,w.calledRun=1,oa||(Da(ta),ea(w),Da(ua)))}}pc();moduleRtn=ha;


  return moduleRtn;
}
);
})();
export default skwasm_st;
