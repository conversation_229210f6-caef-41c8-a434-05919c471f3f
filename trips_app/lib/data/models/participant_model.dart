import '../../domain/entities/participant.dart';

class ParticipantModel extends Participant {
  const ParticipantModel({
    required String name,
    required String avatarUrl,
  }) : super(name: name, avatarUrl: avatarUrl);

  factory ParticipantModel.fromJson(Map<String, dynamic> json) {
    return ParticipantModel(
      name: json['name'] as String,
      avatarUrl: json['avatar_url'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'avatar_url': avatarUrl,
    };
  }

  Participant toEntity() => Participant(
        name: name,
        avatarUrl: avatarUrl,
      );
}
