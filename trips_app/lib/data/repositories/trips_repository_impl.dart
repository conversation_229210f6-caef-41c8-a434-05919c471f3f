import '../../domain/entities/trip.dart';
import '../../domain/repositories/trips_repository.dart';
import '../datasources/trips_local_datasource.dart';

class TripsRepositoryImpl implements TripsRepository {
  final TripsLocalDataSource localDataSource;

  const TripsRepositoryImpl({
    required this.localDataSource,
  });

  @override
  Future<List<Trip>> getTrips() async {
    try {
      final tripsResponse = await localDataSource.getTrips();
      return tripsResponse.trips.map((tripModel) => tripModel.toEntity()).toList();
    } catch (e) {
      throw Exception('Failed to get trips: $e');
    }
  }
}
