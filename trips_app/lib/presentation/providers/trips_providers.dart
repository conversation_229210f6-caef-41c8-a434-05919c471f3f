import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/datasources/trips_local_datasource.dart';
import '../../data/repositories/trips_repository_impl.dart';
import '../../domain/repositories/trips_repository.dart';
import '../../domain/usecases/get_trips_usecase.dart';
import '../viewmodels/trips_viewmodel.dart';

// Data Source Provider
final tripsLocalDataSourceProvider = Provider<TripsLocalDataSource>((ref) {
  return TripsLocalDataSourceImpl();
});

// Repository Provider
final tripsRepositoryProvider = Provider<TripsRepository>((ref) {
  final localDataSource = ref.read(tripsLocalDataSourceProvider);
  return TripsRepositoryImpl(localDataSource: localDataSource);
});

// Use Case Provider
final getTripsUseCaseProvider = Provider<GetTripsUseCase>((ref) {
  final repository = ref.read(tripsRepositoryProvider);
  return GetTripsUseCase(repository);
});

// ViewModel Provider
final tripsViewModelProvider = StateNotifierProvider<TripsViewModel, TripsState>((ref) {
  final getTripsUseCase = ref.read(getTripsUseCaseProvider);
  return TripsViewModel(getTripsUseCase: getTripsUseCase);
});
