class Participant {
  final String name;
  final String avatarUrl;

  const Participant({
    required this.name,
    required this.avatarUrl,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Participant &&
        other.name == name &&
        other.avatarUrl == avatarUrl;
  }

  @override
  int get hashCode => name.hashCode ^ avatarUrl.hashCode;

  @override
  String toString() => 'Participant(name: $name, avatarUrl: $avatarUrl)';
}
