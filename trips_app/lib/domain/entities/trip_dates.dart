class TripDates {
  final DateTime start;
  final DateTime end;

  const TripDates({
    required this.start,
    required this.end,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TripDates &&
        other.start == start &&
        other.end == end;
  }

  @override
  int get hashCode => start.hashCode ^ end.hashCode;

  @override
  String toString() => 'TripDates(start: $start, end: $end)';
}
