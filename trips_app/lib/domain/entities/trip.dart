import 'participant.dart';
import 'trip_dates.dart';

enum TripStatus {
  proposalSent('Proposal Sent'),
  pendingApproval('Pending Approval'),
  readyForTravel('Ready for travel');

  const TripStatus(this.displayName);
  final String displayName;

  static TripStatus fromString(String status) {
    switch (status) {
      case 'Proposal Sent':
        return TripStatus.proposalSent;
      case 'Pending Approval':
        return TripStatus.pendingApproval;
      case 'Ready for travel':
        return TripStatus.readyForTravel;
      default:
        throw ArgumentError('Unknown trip status: $status');
    }
  }
}

class Trip {
  final String id;
  final TripStatus status;
  final String title;
  final TripDates dates;
  final List<Participant> participants;
  final int unfinishedTasks;
  final String coverImage;

  const Trip({
    required this.id,
    required this.status,
    required this.title,
    required this.dates,
    required this.participants,
    required this.unfinishedTasks,
    required this.coverImage,
  });
}
