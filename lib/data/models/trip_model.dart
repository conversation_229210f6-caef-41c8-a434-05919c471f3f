import '../../domain/entities/trip.dart';
import 'participant_model.dart';
import 'trip_dates_model.dart';

class TripModel {
  final String id;
  final String status;
  final String title;
  final TripDatesModel dates;
  final List<ParticipantModel> participants;
  final int unfinishedTasks;
  final String coverImage;

  const TripModel({
    required this.id,
    required this.status,
    required this.title,
    required this.dates,
    required this.participants,
    required this.unfinishedTasks,
    required this.coverImage,
  });

  factory TripModel.fromJson(Map<String, dynamic> json) {
    return TripModel(
      id: json['id'] as String,
      status: json['status'] as String,
      title: json['title'] as String,
      dates: TripDatesModel.fromJson(json['dates'] as Map<String, dynamic>),
      participants: (json['participants'] as List)
          .map((p) => ParticipantModel.fromJson(p as Map<String, dynamic>))
          .toList(),
      unfinishedTasks: json['unfinished_tasks'] as int,
      coverImage: json['cover_image'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'status': status,
      'title': title,
      'dates': dates.toJson(),
      'participants': participants.map((p) => p.toJson()).toList(),
      'unfinished_tasks': unfinishedTasks,
      'cover_image': coverImage,
    };
  }

  Trip toEntity() => Trip(
        id: id,
        status: TripStatus.fromString(status),
        title: title,
        dates: dates.toEntity(),
        participants: participants.map((p) => p.toEntity()).toList(),
        unfinishedTasks: unfinishedTasks,
        coverImage: coverImage,
      );
}
