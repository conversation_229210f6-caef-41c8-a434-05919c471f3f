import 'package:intl/intl.dart';
import '../../domain/entities/trip_dates.dart';

class TripDatesModel extends TripDates {
  const TripDatesModel({
    required DateTime start,
    required DateTime end,
  }) : super(start: start, end: end);

  factory TripDatesModel.fromJson(Map<String, dynamic> json) {
    return TripDatesModel(
      start: _dateFromString(json['start'] as String),
      end: _dateFromString(json['end'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'start': _dateToString(start),
      'end': _dateToString(end),
    };
  }

  TripDates toEntity() => TripDates(
        start: start,
        end: end,
      );

  static DateTime _dateFromString(String dateString) {
    final formatter = DateFormat('dd-MM-yyyy');
    return formatter.parse(dateString);
  }

  static String _dateToString(DateTime date) {
    final formatter = DateFormat('dd-MM-yyyy');
    return formatter.format(date);
  }
}
