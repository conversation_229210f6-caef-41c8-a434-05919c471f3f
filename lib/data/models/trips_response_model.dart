import 'trip_model.dart';

class TripsResponseModel {
  final List<TripModel> trips;

  const TripsResponseModel({
    required this.trips,
  });

  factory TripsResponseModel.fromJson(Map<String, dynamic> json) {
    return TripsResponseModel(
      trips: (json['trips'] as List)
          .map((trip) => TripModel.fromJson(trip as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'trips': trips.map((trip) => trip.toJson()).toList(),
    };
  }
}
