import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/trips_response_model.dart';

abstract class TripsLocalDataSource {
  Future<TripsResponseModel> getTrips();
}

class TripsLocalDataSourceImpl implements TripsLocalDataSource {
  @override
  Future<TripsResponseModel> getTrips() async {
    try {
      final String jsonString = await rootBundle.loadString('assets/trips_mock.json');
      final Map<String, dynamic> jsonMap = json.decode(jsonString);
      return TripsResponseModel.fromJson(jsonMap);
    } catch (e) {
      throw Exception('Failed to load trips data: $e');
    }
  }
}
