import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/trip.dart';
import '../../domain/usecases/get_trips_usecase.dart';

class TripsState {
  final List<Trip> trips;
  final bool isLoading;
  final String? error;

  const TripsState({
    this.trips = const [],
    this.isLoading = false,
    this.error,
  });

  TripsState copyWith({
    List<Trip>? trips,
    bool? isLoading,
    String? error,
  }) {
    return TripsState(
      trips: trips ?? this.trips,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

class TripsViewModel extends StateNotifier<TripsState> {
  final GetTripsUseCase getTripsUseCase;

  TripsViewModel({
    required this.getTripsUseCase,
  }) : super(const TripsState());

  Future<void> loadTrips() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final trips = await getTripsUseCase();
      state = state.copyWith(
        trips: trips,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }
}
